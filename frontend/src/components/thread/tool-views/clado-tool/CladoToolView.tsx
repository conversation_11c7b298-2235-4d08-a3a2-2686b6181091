import React, { useMemo } from 'react';
import {
  CheckCircle,
  AlertTriangle,
  Users,
  Building2,
  Mail,
  Phone,
  Globe,
  MapPin,
  Calendar,
  Briefcase,
  GraduationCap,
  Heart,
  MessageCircle,
  Share,
  ThumbsUp,
  Clock,
  Search,
  User,
  ExternalLink,
  ChevronRight,
  Loader2,
  Eye,
  Star,
  TrendingUp,
  Database,
  FileText,
  Link
} from 'lucide-react';
import { TbDeviceDesktopSearch } from 'react-icons/tb';
import { ToolViewProps } from '../types';
import { formatTimestamp, getToolTitle } from '../utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from '@/lib/utils';
import {
  extractCladoData,
  CladoUserResult,
  CladoCompanyResult,
  CladoProfile,
  CladoExperience,
  CladoEducation,
  CladoContact,
  CladoSocialMedia,
  CladoReaction,
  CladoDeepResearchJob
} from './_utils';

// Helper function to format dates
function formatDate(dateStr: string): string {
  if (!dateStr || dateStr === '1970-01-01T00:00:00') return 'Present';
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short'
    });
  } catch {
    return dateStr;
  }
}

// Helper function to get endpoint-specific icon
function getEndpointIcon(toolName: string | null) {
  switch (toolName) {
    case 'search_linkedin_users':
      return Users;
    case 'search_linkedin_companies':
      return Building2;
    case 'enrich_linkedin_profile':
      return User;
    case 'get_linkedin_contacts':
      return Mail;
    case 'scrape_linkedin_profile':
      return Eye;
    case 'get_linkedin_post_reactions':
      return Heart;
    case 'start_deep_research':
      return Database;
    case 'get_deep_research_status':
      return Clock;
    default:
      return TbDeviceDesktopSearch;
  }
}

// Helper function to get endpoint display name
function getEndpointDisplayName(toolName: string | null): string {
  switch (toolName) {
    case 'search_linkedin_users':
      return 'User Search';
    case 'search_linkedin_companies':
      return 'Company Search';
    case 'enrich_linkedin_profile':
      return 'Profile Enrichment';
    case 'get_linkedin_contacts':
      return 'Contact Retrieval';
    case 'scrape_linkedin_profile':
      return 'Profile Scraping';
    case 'get_linkedin_post_reactions':
      return 'Post Reactions';
    case 'start_deep_research':
      return 'Deep Research';
    case 'get_deep_research_status':
      return 'Research Status';
    default:
      return 'SourcingLeads';
  }
}

// Component for rendering user profile cards
function UserProfileCard({ user }: { user: CladoUserResult }) {
  const profile = user.profile;
  if (!profile) return null;

  return (
    <div className="p-4 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 hover:bg-zinc-50 dark:hover:bg-zinc-800/50 transition-colors">
      <div className="flex items-start gap-4">
        {profile.profile_picture_url && (
          <img
            src={profile.profile_picture_url}
            alt={profile.name || 'Profile'}
            className="w-12 h-12 rounded-full object-cover border-2 border-zinc-200 dark:border-zinc-700"
          />
        )}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-semibold text-zinc-900 dark:text-zinc-100 truncate">
              {profile.name || 'Unknown'}
            </h4>
            {profile.linkedin_url && (
              <a
                href={profile.linkedin_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:text-blue-600 dark:text-blue-400"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            )}
          </div>

          {profile.headline && (
            <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-2 line-clamp-2">
              {profile.headline}
            </p>
          )}

          <div className="flex flex-wrap gap-2 text-xs text-zinc-500 dark:text-zinc-400">
            {profile.location && (
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{profile.location}</span>
              </div>
            )}
            {profile.title && (
              <div className="flex items-center gap-1">
                <Briefcase className="h-3 w-3" />
                <span>{profile.title}</span>
              </div>
            )}
          </div>

          {/* Criteria matching */}
          {profile.criteria && Object.keys(profile.criteria).length > 0 && (
            <div className="mt-3 flex flex-wrap gap-1">
              {Object.entries(profile.criteria).map(([criterion, match]) => (
                <Badge
                  key={criterion}
                  variant="outline"
                  className={cn(
                    "text-xs",
                    match === 'YES' && "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300",
                    match === 'MAYBE' && "bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300",
                    match === 'NO' && "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300"
                  )}
                >
                  {criterion}: {match}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Component for rendering company cards
function CompanyCard({ company }: { company: CladoCompanyResult }) {
  return (
    <div className="p-4 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 hover:bg-zinc-50 dark:hover:bg-zinc-800/50 transition-colors">
      <div className="flex items-start gap-4">
        {company.logo_url && (
          <img
            src={company.logo_url}
            alt={company.name || 'Company'}
            className="w-12 h-12 rounded-lg object-cover border border-zinc-200 dark:border-zinc-700"
          />
        )}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-semibold text-zinc-900 dark:text-zinc-100 truncate">
              {company.name || 'Unknown Company'}
            </h4>
            {company.linkedin_url && (
              <a
                href={company.linkedin_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:text-blue-600 dark:text-blue-400"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            )}
          </div>

          {company.description && (
            <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-2 line-clamp-2">
              {company.description}
            </p>
          )}

          <div className="flex flex-wrap gap-2 text-xs text-zinc-500 dark:text-zinc-400">
            {company.industry && (
              <div className="flex items-center gap-1">
                <Building2 className="h-3 w-3" />
                <span>{company.industry}</span>
              </div>
            )}
            {company.location && (
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{company.location}</span>
              </div>
            )}
            {company.size && (
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>{company.size}</span>
              </div>
            )}
            {company.website && (
              <a
                href={company.website}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 text-blue-500 hover:text-blue-600 dark:text-blue-400"
              >
                <Globe className="h-3 w-3" />
                <span>Website</span>
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Component for rendering contact information
function ContactInfo({ contacts, socialMedia }: { contacts?: CladoContact[], socialMedia?: CladoSocialMedia[] }) {
  return (
    <div className="space-y-4">
      {contacts && contacts.length > 0 && (
        <div>
          <h5 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2 flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Contact Information
          </h5>
          <div className="space-y-2">
            {contacts.map((contact, idx) => (
              <div key={idx} className="flex items-center justify-between p-2 bg-zinc-50 dark:bg-zinc-800 rounded">
                <div className="flex items-center gap-2">
                  {contact.type === 'email' ? <Mail className="h-3 w-3" /> : <Phone className="h-3 w-3" />}
                  <span className="text-sm text-zinc-700 dark:text-zinc-300">{contact.value}</span>
                </div>
                {contact.confidence && (
                  <Badge variant="outline" className="text-xs">
                    {Math.round(contact.confidence * 100)}% confidence
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {socialMedia && socialMedia.length > 0 && (
        <div>
          <h5 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2 flex items-center gap-2">
            <Link className="h-4 w-4" />
            Social Media
          </h5>
          <div className="space-y-2">
            {socialMedia.map((social, idx) => (
              <div key={idx} className="flex items-center gap-2 p-2 bg-zinc-50 dark:bg-zinc-800 rounded">
                <Globe className="h-3 w-3" />
                <a
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-500 hover:text-blue-600 dark:text-blue-400 truncate"
                >
                  {social.platform}: {social.username || social.url}
                </a>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Component for rendering profile enrichment data
function ProfileEnrichment({ profileData }: { profileData: any }) {
  if (!profileData || typeof profileData !== 'object') return null;

  const profile = profileData.profile;
  const experience = profileData.experience || [];
  const education = profileData.education || [];

  return (
    <div className="space-y-6">
      {/* Profile Summary */}
      {profile && (
        <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-start gap-4">
            {profile.profile_picture_url && (
              <img
                src={profile.profile_picture_url}
                alt={profile.name || 'Profile'}
                className="w-16 h-16 rounded-full object-cover border-2 border-white shadow-lg"
              />
            )}
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-1">
                {profile.name || 'Unknown'}
              </h3>
              {profile.headline && (
                <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-2">
                  {profile.headline}
                </p>
              )}
              <div className="flex flex-wrap gap-3 text-xs text-zinc-500 dark:text-zinc-400">
                {profile.location && (
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    <span>{profile.location}</span>
                  </div>
                )}
                {profile.linkedin_url && (
                  <a
                    href={profile.linkedin_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-blue-500 hover:text-blue-600 dark:text-blue-400"
                  >
                    <ExternalLink className="h-3 w-3" />
                    <span>LinkedIn Profile</span>
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Experience */}
      {experience.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3 flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            Experience
          </h4>
          <div className="space-y-3">
            {experience.map((exp: CladoExperience, idx: number) => (
              <div key={idx} className="p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800">
                <div className="flex items-start gap-3">
                  {exp.company_logo && (
                    <img
                      src={exp.company_logo}
                      alt={exp.company_name || 'Company'}
                      className="w-8 h-8 rounded object-cover"
                    />
                  )}
                  <div className="flex-1">
                    <h5 className="font-medium text-zinc-900 dark:text-zinc-100">
                      {exp.title || 'Unknown Position'}
                    </h5>
                    <p className="text-sm text-zinc-600 dark:text-zinc-400">
                      {exp.company_name || 'Unknown Company'}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-zinc-500 dark:text-zinc-400 mt-1">
                      <Calendar className="h-3 w-3" />
                      <span>
                        {formatDate(exp.start_date || '')} - {formatDate(exp.end_date || '')}
                      </span>
                      {exp.location && (
                        <>
                          <span>•</span>
                          <span>{exp.location}</span>
                        </>
                      )}
                    </div>
                    {exp.description && (
                      <p className="text-xs text-zinc-600 dark:text-zinc-400 mt-2 line-clamp-2">
                        {exp.description}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Education */}
      {education.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3 flex items-center gap-2">
            <GraduationCap className="h-4 w-4" />
            Education
          </h4>
          <div className="space-y-3">
            {education.map((edu: CladoEducation, idx: number) => (
              <div key={idx} className="p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800">
                <div className="flex items-start gap-3">
                  {edu.school_logo && (
                    <img
                      src={edu.school_logo}
                      alt={edu.school_name || 'School'}
                      className="w-8 h-8 rounded object-cover"
                    />
                  )}
                  <div className="flex-1">
                    <h5 className="font-medium text-zinc-900 dark:text-zinc-100">
                      {edu.degree || 'Unknown Degree'}
                    </h5>
                    <p className="text-sm text-zinc-600 dark:text-zinc-400">
                      {edu.school_name || 'Unknown School'}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-zinc-500 dark:text-zinc-400 mt-1">
                      <Calendar className="h-3 w-3" />
                      <span>
                        {formatDate(edu.start_date || '')} - {formatDate(edu.end_date || '')}
                      </span>
                      {edu.field_of_study && (
                        <>
                          <span>•</span>
                          <span>{edu.field_of_study}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export function CladoToolView({
  name = 'clado-tool',
  assistantContent,
  toolContent,
  assistantTimestamp,
  toolTimestamp,
  isSuccess = true,
  isStreaming = false,
}: ToolViewProps) {
  // Use helper to parse Clado responses
  const cladoData = useMemo(() =>
    extractCladoData(
      assistantContent,
      toolContent,
      isSuccess,
      toolTimestamp,
      assistantTimestamp,
    ),
  [assistantContent, toolContent, isSuccess, toolTimestamp, assistantTimestamp]);

  const {
    toolName,
    query,
    results,
    cost,
    status,
    totalResults,
    jobId,
    actualIsSuccess,
    actualToolTimestamp,
    actualAssistantTimestamp,
  } = cladoData;

  const displayTimestamp = actualToolTimestamp || actualAssistantTimestamp;
  const endpointIcon = getEndpointIcon(toolName);
  const endpointName = getEndpointDisplayName(toolName);

  // Determine result type and render appropriate component
  const renderResults = () => {
    if (!results) return null;

    // First, try to detect result type based on data structure rather than just toolName
    // This is more robust for cases where toolName might not be detected correctly

    // Check if it's user search results (array of users with profile data)
    if (Array.isArray(results) && results.length > 0 && results[0]?.profile) {
      return (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center gap-2">
              <Users className="h-4 w-4" />
              User Results
            </h4>
            {totalResults && (
              <Badge variant="outline" className="text-xs">
                {totalResults} total
              </Badge>
            )}
          </div>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {results.map((user: CladoUserResult, idx: number) => (
              <UserProfileCard key={idx} user={user} />
            ))}
          </div>
        </div>
      );
    }

    // Check if it's company search results (array of companies)
    if (Array.isArray(results) && results.length > 0 && (results[0]?.name || results[0]?.company_name) && !results[0]?.profile) {
      return (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Company Results
            </h4>
            {totalResults && (
              <Badge variant="outline" className="text-xs">
                {totalResults} total
              </Badge>
            )}
          </div>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {results.map((company: CladoCompanyResult, idx: number) => (
              <CompanyCard key={idx} company={company} />
            ))}
          </div>
        </div>
      );
    }

    // Check if it's profile enrichment data (single profile with experience/education)
    if (typeof results === 'object' && !Array.isArray(results) &&
        (results.profile || results.experience || results.education || results.name || results.headline)) {
      return <ProfileEnrichment profileData={results} />;
    }

    // Check if it's contact information
    if (typeof results === 'object' && !Array.isArray(results) && results.contacts) {
      return <ContactInfo contacts={results.contacts} socialMedia={results.social_media} />;
    }

    // Now fall back to toolName-based detection for specific cases
    switch (toolName) {
      case 'search_linkedin_users':
        if (Array.isArray(results)) {
          return (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  User Results
                </h4>
                {totalResults && (
                  <Badge variant="outline" className="text-xs">
                    {totalResults} total
                  </Badge>
                )}
              </div>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {results.map((user: CladoUserResult, idx: number) => (
                  <UserProfileCard key={idx} user={user} />
                ))}
              </div>
            </div>
          );
        }
        break;

      case 'search_linkedin_companies':
        if (Array.isArray(results)) {
          return (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Company Results
                </h4>
                {totalResults && (
                  <Badge variant="outline" className="text-xs">
                    {totalResults} total
                  </Badge>
                )}
              </div>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {results.map((company: CladoCompanyResult, idx: number) => (
                  <CompanyCard key={idx} company={company} />
                ))}
              </div>
            </div>
          );
        }
        break;

      case 'enrich_linkedin_profile':
      case 'scrape_linkedin_profile':
        if (Array.isArray(results) && results.length > 0) {
          return <ProfileEnrichment profileData={results[0]} />;
        } else if (typeof results === 'object' && !Array.isArray(results)) {
          return <ProfileEnrichment profileData={results} />;
        }
        break;

      case 'get_linkedin_contacts':
        if (typeof results === 'object' && results.contacts) {
          return <ContactInfo contacts={results.contacts} socialMedia={results.social_media} />;
        }
        break;

      case 'get_linkedin_post_reactions':
        if (Array.isArray(results)) {
          return (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center gap-2">
                <Heart className="h-4 w-4" />
                Post Reactions
              </h4>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {results.map((reaction: CladoReaction, idx: number) => (
                  <div key={idx} className="flex items-center gap-3 p-2 bg-zinc-50 dark:bg-zinc-800 rounded">
                    <div className="flex items-center gap-2">
                      {reaction.type === 'LIKE' && <ThumbsUp className="h-4 w-4 text-blue-500" />}
                      {reaction.type === 'LOVE' && <Heart className="h-4 w-4 text-red-500" />}
                      {reaction.type === 'CELEBRATE' && <Star className="h-4 w-4 text-yellow-500" />}
                      <span className="text-xs text-zinc-500">{reaction.type}</span>
                    </div>
                    {reaction.user && (
                      <div className="flex-1">
                        <p className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                          {reaction.user.name}
                        </p>
                        {reaction.user.headline && (
                          <p className="text-xs text-zinc-500 dark:text-zinc-400 truncate">
                            {reaction.user.headline}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        }
        break;

      case 'start_deep_research':
        return (
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-2">
              <Database className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
                Deep Research Started
              </span>
            </div>
            {jobId && (
              <p className="text-xs text-blue-700 dark:text-blue-300 font-mono">
                Job ID: {jobId}
              </p>
            )}
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              Use get_deep_research_status to check progress
            </p>
          </div>
        );

      case 'get_deep_research_status':
        if (typeof results === 'object') {
          const job = results as CladoDeepResearchJob;
          return (
            <div className="space-y-4">
              <div className="p-4 bg-zinc-50 dark:bg-zinc-800 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                    Research Job Status
                  </span>
                  <Badge
                    variant="outline"
                    className={cn(
                      "text-xs",
                      status === 'completed' && "bg-green-50 text-green-700 border-green-200",
                      status === 'processing' && "bg-yellow-50 text-yellow-700 border-yellow-200",
                      status === 'pending' && "bg-blue-50 text-blue-700 border-blue-200",
                      status === 'error' && "bg-red-50 text-red-700 border-red-200"
                    )}
                  >
                    {status}
                  </Badge>
                </div>
                {jobId && (
                  <p className="text-xs text-zinc-500 dark:text-zinc-400 font-mono mb-1">
                    Job ID: {jobId}
                  </p>
                )}
                {job.query && (
                  <p className="text-xs text-zinc-600 dark:text-zinc-400">
                    Query: {job.query}
                  </p>
                )}
              </div>

              {job.results && Array.isArray(job.results) && job.results.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                      Research Results
                    </h4>
                    <Badge variant="outline" className="text-xs">
                      {job.total || job.results.length} results
                    </Badge>
                  </div>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {job.results.map((user: CladoUserResult, idx: number) => (
                      <UserProfileCard key={idx} user={user} />
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        }
        break;

      default:
        // Fallback for unknown result types
        return (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Results</h4>
            <ScrollArea className="max-h-72 border rounded-md p-3">
              <pre className="text-xs text-zinc-700 dark:text-zinc-200 whitespace-pre-wrap">
                {typeof results === 'object' ? JSON.stringify(results, null, 2) : String(results)}
              </pre>
            </ScrollArea>
          </div>
        );
    }

    return null;
  };

  return (
    <Card className="gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-white dark:bg-zinc-950">
      {/* Header */}
      <CardHeader className="h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2">
        <div className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="relative p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20">
              {React.createElement(endpointIcon, { className: 'w-5 h-5 text-purple-500 dark:text-purple-400' })}
            </div>
            <div>
              <CardTitle className="text-base font-medium text-zinc-900 dark:text-zinc-100">
                {endpointName}
              </CardTitle>
              {toolName && toolName !== name && (
                <p className="text-xs text-zinc-500 dark:text-zinc-400 font-mono">
                  {toolName}
                </p>
              )}
            </div>
          </div>
          {!isStreaming && (
            <Badge
              variant="secondary"
              className={cn(
                'text-xs font-medium',
                actualIsSuccess
                  ? 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800'
                  : 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800',
              )}
            >
              {actualIsSuccess ? (
                <CheckCircle className="h-3 w-3 mr-1" />
              ) : (
                <AlertTriangle className="h-3 w-3 mr-1" />
              )}
              {actualIsSuccess ? 'Success' : 'Failed'}
            </Badge>
          )}
        </div>
      </CardHeader>

      {/* Body */}
      <CardContent className="p-0 h-full flex-1 overflow-hidden relative">
        {isStreaming ? (
          <div className="flex flex-col items-center justify-center h-full py-8 px-6">
            <div className="text-center w-full max-w-xs">
              <div className="w-16 h-16 rounded-xl mx-auto mb-4 flex items-center justify-center bg-purple-100 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800">
                <Loader2 className="h-8 w-8 animate-spin text-purple-500 dark:text-purple-400" />
              </div>
              <h3 className="text-base font-medium text-zinc-900 dark:text-zinc-100 mb-2">
                Processing LinkedIn research...
              </h3>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                {query ? `Searching: ${query}` : 'Executing Clado operation'}
              </p>
            </div>
          </div>
        ) : (
          <div className="p-4 space-y-6">
            {/* Query */}
            {query && (
              <div className="p-3 bg-zinc-50 dark:bg-zinc-900/50 rounded-lg border border-zinc-200 dark:border-zinc-800">
                <div className="flex items-center gap-2 mb-1">
                  <Search className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
                  <span className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Query</span>
                </div>
                <p className="text-sm text-zinc-800 dark:text-zinc-200">{query}</p>
              </div>
            )}

            {/* Status and Cost */}
            {(status || cost || jobId) && (
              <div className="flex flex-wrap gap-3">
                {status && (
                  <Badge
                    variant="outline"
                    className={cn(
                      "text-xs",
                      status === 'completed' && "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300",
                      status === 'processing' && "bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300",
                      status === 'pending' && "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300",
                      status === 'error' && "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300"
                    )}
                  >
                    <Clock className="h-3 w-3 mr-1" />
                    {status}
                  </Badge>
                )}
                {cost && (
                  <Badge variant="outline" className="text-xs">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    {cost}
                  </Badge>
                )}
                {jobId && (
                  <Badge variant="outline" className="text-xs font-mono">
                    <FileText className="h-3 w-3 mr-1" />
                    {jobId}
                  </Badge>
                )}
              </div>
            )}

            {/* Results */}
            {renderResults()}

            {/* No results message */}
            {!results && !isStreaming && actualIsSuccess && (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="w-12 h-12 rounded-lg bg-zinc-100 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 flex items-center justify-center mb-3">
                  <Search className="h-6 w-6 text-zinc-400" />
                </div>
                <p className="text-sm text-zinc-500 dark:text-zinc-400">
                  No results found for this query
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Footer */}
      <div className="px-4 py-2 h-10 bg-zinc-50/50 dark:bg-zinc-900/50 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4">
        <div className="flex items-center gap-2 text-xs text-zinc-500 dark:text-zinc-400">
          {endpointName !== 'SourcingLeads' && (
            <Badge variant="outline" className="h-6 py-0.5 text-xs">
              <TbDeviceDesktopSearch className="h-3 w-3 mr-1" />
              SourcingLeads
            </Badge>
          )}
        </div>
        <div className="text-xs text-zinc-500 dark:text-zinc-400">
          {displayTimestamp ? formatTimestamp(displayTimestamp) : ''}
        </div>
      </div>
    </Card>
  );
}
