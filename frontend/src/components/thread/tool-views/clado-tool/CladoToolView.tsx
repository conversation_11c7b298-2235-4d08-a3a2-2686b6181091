import React, { useMemo } from 'react';
import {
  Check<PERSON>ir<PERSON>,
  AlertTriangle,
} from 'lucide-react';
import { TbDeviceDesktopSearch } from 'react-icons/tb';
import { ToolViewProps } from '../types';
import { formatTimestamp, getToolTitle } from '../utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from '@/lib/utils';
import { extractCladoData } from './_utils';

function pretty(content: any): string {
  if (!content) return '';
  if (typeof content === 'string') {
    try {
      return JSON.stringify(JSON.parse(content), null, 2);
    } catch {
      return content;
    }
  }
  return JSON.stringify(content, null, 2);
}

// helper to parse safely
function safeParse(json: any): any {
  if (!json) return null;
  if (typeof json === 'string') {
    try {
      return JSON.parse(json);
    } catch {
      return null;
    }
  }
  return json;
}

export function CladoToolView({
  name = 'clado-tool',
  assistantContent,
  toolContent,
  assistantTimestamp,
  toolTimestamp,
  isSuccess = true,
  isStreaming = false,
}: ToolViewProps) {
  const toolTitle = getToolTitle(name);
  const headerIcon = TbDeviceDesktopSearch;

  // Use helper to parse Clado / FindAnything responses
  const cladoData = useMemo(() =>
    extractCladoData(
      assistantContent,
      toolContent,
      isSuccess,
      toolTimestamp,
      assistantTimestamp,
    ),
  [assistantContent, toolContent, isSuccess, toolTimestamp, assistantTimestamp]);

  const {
    query,
    results,
    cost,
    status,
    actualIsSuccess,
    actualToolTimestamp,
    actualAssistantTimestamp,
  } = cladoData;

  const displayTimestamp = actualToolTimestamp || actualAssistantTimestamp;

  return (
    <Card className="gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-white dark:bg-zinc-950">
      {/* Header */}
      <CardHeader className="h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2">
        <div className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="relative p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20">
              {React.createElement(headerIcon, { className: 'w-5 h-5 text-purple-500 dark:text-purple-400' })}
            </div>
            <div>
              <CardTitle className="text-base font-medium text-zinc-900 dark:text-zinc-100">
                {toolTitle}
              </CardTitle>
            </div>
          </div>
          {!isStreaming && (
            <Badge
              variant="secondary"
              className={cn(
                'text-xs font-medium',
                actualIsSuccess
                  ? 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800'
                  : 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800',
              )}
            >
              {actualIsSuccess ? (
                <CheckCircle className="h-3 w-3 mr-1" />
              ) : (
                <AlertTriangle className="h-3 w-3 mr-1" />
              )}
              {actualIsSuccess ? 'Success' : 'Failed'}
            </Badge>
          )}
        </div>
      </CardHeader>

      {/* Body */}
      <CardContent className="p-0 h-full flex-1 overflow-hidden relative">
        <div className="p-4 space-y-6">
          {/* Query */}
          {query && (
            <div className="space-y-1 text-sm">
              <h4 className="font-medium text-zinc-700 dark:text-zinc-300">Query</h4>
              <p className="text-zinc-800 dark:text-zinc-200 break-all text-xs">{query}</p>
            </div>
          )}

          {/* Status / Cost */}
          {(status || cost) && (
            <div className="flex flex-wrap gap-4 text-xs">
              {status && (
                <div className="flex gap-1 items-center">
                  <span className="font-semibold text-zinc-600 dark:text-zinc-300">Status:</span>
                  <span className="text-zinc-700 dark:text-zinc-200">{String(status)}</span>
                </div>
              )}
              {cost && (
                <div className="flex gap-1 items-center">
                  <span className="font-semibold text-zinc-600 dark:text-zinc-300">Cost:</span>
                  <span className="text-zinc-700 dark:text-zinc-200">{String(cost)}</span>
                </div>
              )}
            </div>
          )}

          {/* Results */}
          {results && (
            <div className="space-y-2">
              <h4 className="flex items-center gap-1 text-sm font-medium text-zinc-700 dark:text-zinc-300">
                <span>Results</span>
              </h4>
              <ScrollArea className="max-h-72 border rounded-md p-2">
                {Array.isArray(results) ? (
                  results.length > 0 ? (
                    <ul className="space-y-2">
                      {results.map((item, idx) => (
                        <li key={idx} className="text-xs text-zinc-700 dark:text-zinc-200 break-all">
                          {typeof item === 'object' ? JSON.stringify(item) : String(item)}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-xs text-zinc-500 dark:text-zinc-400">No results found.</p>
                  )
                ) : (
                  <div className="text-xs text-zinc-700 dark:text-zinc-200 break-all">
                    {typeof results === 'object' ? JSON.stringify(results, null, 2) : String(results)}
                  </div>
                )}
              </ScrollArea>
            </div>
          )}
        </div>
      </CardContent>

      {/* Footer */}
      <div className="px-4 py-2 h-10 bg-zinc-50/50 dark:bg-zinc-900/50 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4 text-xs text-zinc-500 dark:text-zinc-400">
        <span>{displayTimestamp ? formatTimestamp(displayTimestamp) : ''}</span>
      </div>
    </Card>
  );
}
