import { extractToolData, normalizeContentToString } from '../utils';

// Utility to parse Clado tool responses
export function extractCladoData(
  assistantContent: any,
  toolContent: any,
  isSuccess: boolean,
  toolTimestamp?: string,
  assistantTimestamp?: string
): {
  query: string | null;
  results: any;
  cost: string | null;
  status: string | null;
  actualIsSuccess: boolean;
  actualToolTimestamp?: string;
  actualAssistantTimestamp?: string;
} {
  let query: string | null = null;
  let results: any = null;
  let cost: string | null = null;
  let status: string | null = null;
  let actualIsSuccess = isSuccess;
  let actualToolTimestamp = toolTimestamp;
  let actualAssistantTimestamp = assistantTimestamp;

  const parseContent = (content: any) => {
    if (!content) return null;

    // First, try to extract via standard tool parser
    const { toolResult } = extractToolData(content);
    if (toolResult) {
      // If toolOutput exists, attempt to parse it for Clado data
      const outputStr = normalizeContentToString(toolResult.toolOutput);
      if (outputStr) {
        try {
          const parsedOutput = JSON.parse(outputStr);
          return parsedOutput;
        } catch {
          // non-JSON output; return as string for display
          return { raw: outputStr };
        }
      }
      return toolResult; // fallback to raw toolResult
    }

    // Fallback: attempt JSON.parse on string
    const contentStr = normalizeContentToString(content);
    try {
      return JSON.parse(contentStr);
    } catch {
      // Fallback: return raw string so UI can display it
      return contentStr;
    }
  };

  const parsedAssistant = parseContent(assistantContent);
  const parsedTool = parseContent(toolContent);

  // Extract arguments for fallback (query etc.)
  const { arguments: assistantArgs } = extractToolData(assistantContent || {});

  const merged: any = parsedTool || parsedAssistant;
  if (merged && typeof merged === 'object') {
    /*
     * Determine query
     */
    query = merged.query ?? merged.input ?? query;

    /*
     * Determine results – prioritise explicit `results` array but fall back to other common keys
     */
    results =
      merged.results ??
      merged.profile_data ??
      merged.contacts ??
      merged.social_media ??
      merged.data ??
      merged.items ??
      results;

    /*
     * Determine cost.  We only pick up strings that clearly represent credit usage to avoid
     * accidentally assigning endpoint paths (e.g. "/api/search/…").  Accept if it contains
     * the word "credit" (case-insensitive) or is purely numeric.
     */
    if (typeof merged.cost === 'string') {
      const costCandidate = merged.cost.trim();
      if (/credit/i.test(costCandidate) || /\d+/.test(costCandidate)) {
        cost = costCandidate;
      }
    }

    /* status & success flags */
    status = merged.status ?? merged.state ?? status;
    if (typeof merged.success === 'boolean') actualIsSuccess = merged.success;
    if (merged.timestamp) actualToolTimestamp = merged.timestamp;
  }

  // Fallback to assistant arguments if query still null
  if (!query && assistantArgs?.query) {
    query = assistantArgs.query;
  }

  return {
    query,
    results,
    cost,
    status,
    actualIsSuccess,
    actualToolTimestamp,
    actualAssistantTimestamp,
  };
}
