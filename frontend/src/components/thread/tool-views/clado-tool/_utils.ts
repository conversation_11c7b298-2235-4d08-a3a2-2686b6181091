import { extractToolData, normalizeContentToString } from '../utils';

// Types for Clado API responses
export interface CladoProfile {
  id?: string;
  name?: string;
  location?: string;
  headline?: string;
  description?: string;
  title?: string;
  profile_picture_url?: string;
  linkedin_url?: string;
  twitter_handle?: string;
  websites?: Array<{ url: string }>;
  criteria?: Record<string, string>;
}

export interface CladoExperience {
  title?: string;
  company_name?: string;
  start_date?: string;
  end_date?: string;
  description?: string;
  location?: string;
  company_logo?: string;
}

export interface CladoEducation {
  degree?: string;
  field_of_study?: string;
  school_name?: string;
  school_logo?: string;
  start_date?: string;
  end_date?: string;
  description?: string;
}

export interface CladoContact {
  type?: string;
  value?: string;
  confidence?: number;
}

export interface CladoSocialMedia {
  platform?: string;
  url?: string;
  username?: string;
}

export interface CladoUserResult {
  profile?: CladoProfile;
  experience?: CladoExperience[];
  education?: CladoEducation[];
  languages?: string[];
}

export interface CladoCompanyResult {
  id?: string;
  name?: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: string;
  location?: string;
  logo_url?: string;
  linkedin_url?: string;
}

export interface CladoReaction {
  type?: string;
  user?: {
    name?: string;
    headline?: string;
    profile_url?: string;
    profile_picture?: string;
  };
}

export interface CladoDeepResearchJob {
  job_id?: string;
  status?: string;
  message?: string;
  query?: string;
  results?: CladoUserResult[];
  total?: number;
  enrichment_stats?: Record<string, any>;
}

// Utility to parse Clado tool responses
export function extractCladoData(
  assistantContent: any,
  toolContent: any,
  isSuccess: boolean,
  toolTimestamp?: string,
  assistantTimestamp?: string
): {
  toolName: string | null;
  query: string | null;
  results: any;
  cost: string | null;
  status: string | null;
  totalResults: number | null;
  jobId: string | null;
  actualIsSuccess: boolean;
  actualToolTimestamp?: string;
  actualAssistantTimestamp?: string;
} {
  const toolName: string | null = null;
  let query: string | null = null;
  let results: any = null;
  let cost: string | null = null;
  let status: string | null = null;
  let totalResults: number | null = null;
  let jobId: string | null = null;
  let actualIsSuccess = isSuccess;
  let actualToolTimestamp = toolTimestamp;
  const actualAssistantTimestamp = assistantTimestamp;

  const parseContent = (content: any) => {
    if (!content) return null;

    // First, try to extract via standard tool parser
    const { toolResult } = extractToolData(content);

    if (toolResult) {
      // If toolOutput exists, attempt to parse it for Clado data
      const outputStr = normalizeContentToString(toolResult.toolOutput);
      if (outputStr) {
        try {
          const parsedOutput = JSON.parse(outputStr);
          return parsedOutput;
        } catch {
          // non-JSON output; return as string for display
          return { raw: outputStr };
        }
      }
      return toolResult; // fallback to raw toolResult
    }

    // Fallback: attempt JSON.parse on string
    const contentStr = normalizeContentToString(content);
    try {
      return JSON.parse(contentStr);
    } catch {
      // Fallback: return raw string so UI can display it
      return contentStr;
    }
  };

  const parsedAssistant = parseContent(assistantContent);
  const parsedTool = parseContent(toolContent);

  // Extract arguments for fallback (query etc.)
  const { arguments: assistantArgs } = extractToolData(assistantContent || {});

  const merged: any = parsedTool || parsedAssistant;
  if (merged && typeof merged === 'object') {
    // Determine query
    query = merged.query ?? merged.input ?? assistantArgs?.query ?? query;

    // Determine results based on endpoint type
    if (merged.results) {
      results = merged.results;
      totalResults = merged.total_results ?? merged.total ?? (Array.isArray(merged.results) ? merged.results.length : null);
    } else if (merged.profile_data) {
      results = merged.profile_data;
    } else if (merged.contacts) {
      results = { contacts: merged.contacts, social_media: merged.social_media };
    } else if (merged.data) {
      results = merged.data;
    } else if (merged.reactions) {
      results = merged.reactions;
      totalResults = merged.pagination?.total_reactions ?? null;
    }

    // Extract job information for deep research
    jobId = merged.job_id ?? jobId;

    // Determine cost - look for credit usage patterns
    if (typeof merged.cost === 'string') {
      const costCandidate = merged.cost.trim();
      if (/credit/i.test(costCandidate) || /^\d+/.test(costCandidate)) {
        cost = costCandidate;
      }
    }

    // Status and success flags
    status = merged.status ?? merged.state ?? status;
    if (typeof merged.success === 'boolean') actualIsSuccess = merged.success;
    if (merged.timestamp) actualToolTimestamp = merged.timestamp;
  }

  return {
    toolName,
    query,
    results,
    cost,
    status,
    totalResults,
    jobId,
    actualIsSuccess,
    actualToolTimestamp,
    actualAssistantTimestamp,
  };
}
